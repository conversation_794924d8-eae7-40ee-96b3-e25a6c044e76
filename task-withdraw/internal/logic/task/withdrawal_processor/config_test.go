package withdrawal_processor

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestNonRetryableErrorsConfiguration tests the nonRetryableErrors configuration
func TestNonRetryableErrorsConfiguration(t *testing.T) {
	t.Run("Insufficient balance should not be in nonRetryableErrors", func(t *testing.T) {
		// Test that insufficient balance errors are not configured as non-retryable
		// This reflects the change where insufficient balance was removed from nonRetryableErrors

		// Simulate the nonRetryableErrors configuration (from config.yaml)
		nonRetryableErrors := []string{
			"invalid address format",
			"token config not found",
			"contract execution failed",
			// Note: "insufficient hot wallet balance" should NOT be here anymore
		}

		// Verify that insufficient balance errors are not in the list
		for _, errPattern := range nonRetryableErrors {
			assert.NotContains(t, errPattern, "insufficient", "nonRetryableErrors should not contain 'insufficient'")
			assert.NotContains(t, errPattern, "balance", "nonRetryableErrors should not contain 'balance'")
		}

		// Verify that other legitimate non-retryable errors are still present
		assert.Contains(t, nonRetryableErrors, "invalid address format", "Should still contain address format errors")
		assert.Contains(t, nonRetryableErrors, "token config not found", "Should still contain token config errors")
	})

	t.Run("Non-retryable error detection logic", func(t *testing.T) {
		// Test the logic for detecting non-retryable errors
		nonRetryableErrors := []string{
			"invalid address format",
			"token config not found",
			"contract execution failed",
		}

		testCases := []struct {
			name                 string
			errorMessage         string
			shouldBeNonRetryable bool
			description          string
		}{
			{
				name:                 "Invalid address error",
				errorMessage:         "invalid address format: 0xinvalid",
				shouldBeNonRetryable: true,
				description:          "Should be detected as non-retryable",
			},
			{
				name:                 "Token config error",
				errorMessage:         "token config not found for symbol XYZ",
				shouldBeNonRetryable: true,
				description:          "Should be detected as non-retryable",
			},
			{
				name:                 "Contract execution error",
				errorMessage:         "contract execution failed: revert",
				shouldBeNonRetryable: true,
				description:          "Should be detected as non-retryable",
			},
			{
				name:                 "Insufficient balance error",
				errorMessage:         "insufficient hot wallet balance for ETH",
				shouldBeNonRetryable: false,
				description:          "Should NOT be detected as non-retryable",
			},
			{
				name:                 "Network timeout error",
				errorMessage:         "network connection timeout",
				shouldBeNonRetryable: false,
				description:          "Should NOT be detected as non-retryable",
			},
			{
				name:                 "Gas limit error",
				errorMessage:         "gas limit exceeded",
				shouldBeNonRetryable: false,
				description:          "Should NOT be detected as non-retryable",
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				// Simulate the non-retryable error detection logic
				isNonRetryable := false
				for _, pattern := range nonRetryableErrors {
					if containsIgnoreCase(tc.errorMessage, pattern) {
						isNonRetryable = true
						break
					}
				}

				assert.Equal(t, tc.shouldBeNonRetryable, isNonRetryable, tc.description)
			})
		}
	})
}

// TestRetryConfiguration tests the retry mechanism configuration
func TestRetryConfiguration(t *testing.T) {
	t.Run("Retry configuration values", func(t *testing.T) {
		// Test the retry configuration structure
		retryConfig := RetryConfig{
			Maxattempts:  30, // From config.yaml
			DelaySeconds: 5,  // Typical delay
			NonRetryableErrors: []string{
				"invalid address format",
				"token config not found",
			},
		}

		assert.Greater(t, retryConfig.Maxattempts, 0, "Max attempts should be positive")
		assert.Greater(t, retryConfig.DelaySeconds, 0, "Delay seconds should be positive")
		assert.NotEmpty(t, retryConfig.NonRetryableErrors, "NonRetryableErrors should not be empty")

		// Verify that insufficient balance is not in the configuration
		for _, errPattern := range retryConfig.NonRetryableErrors {
			assert.NotContains(t, errPattern, "insufficient", "NonRetryableErrors should not contain insufficient balance patterns")
		}
	})

	t.Run("Retry logic for different error types", func(t *testing.T) {
		maxAttempts := 3

		testCases := []struct {
			name                  string
			errorMessage          string
			currentRetries        int
			isInsufficientBalance bool
			isNonRetryable        bool
			shouldRetry           bool
			expectedFinalState    int32
			description           string
		}{
			{
				name:                  "Insufficient balance - no retry",
				errorMessage:          "insufficient balance for ETH",
				currentRetries:        0,
				isInsufficientBalance: true,
				isNonRetryable:        false,
				shouldRetry:           false,
				expectedFinalState:    2, // Processing state
				description:           "Insufficient balance should not retry",
			},
			{
				name:                  "Retryable error - under max attempts",
				errorMessage:          "network timeout",
				currentRetries:        1,
				isInsufficientBalance: false,
				isNonRetryable:        false,
				shouldRetry:           true,
				expectedFinalState:    1, // Pending state
				description:           "Retryable error should retry when under max attempts",
			},
			{
				name:                  "Non-retryable error",
				errorMessage:          "invalid address format",
				currentRetries:        0,
				isInsufficientBalance: false,
				isNonRetryable:        true,
				shouldRetry:           false,
				expectedFinalState:    5, // Failed state
				description:           "Non-retryable error should not retry",
			},
			{
				name:                  "Max retries reached",
				errorMessage:          "network timeout",
				currentRetries:        3,
				isInsufficientBalance: false,
				isNonRetryable:        false,
				shouldRetry:           false,
				expectedFinalState:    5, // Failed state
				description:           "Should not retry when max attempts reached",
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				// Simulate the retry decision logic from processor.go
				var finalState int32
				shouldRetry := false

				if tc.isInsufficientBalance {
					// Special handling for insufficient balance
					finalState = 2 // State 2: Processing
					shouldRetry = false
				} else if !tc.isNonRetryable && tc.currentRetries < maxAttempts {
					// Retryable error and attempts remaining
					finalState = 1 // State 1: Pending
					shouldRetry = true
				} else {
					// Non-retryable error or max retries reached
					finalState = 5 // State 5: Failed
					shouldRetry = false
				}

				assert.Equal(t, tc.shouldRetry, shouldRetry, tc.description)
				assert.Equal(t, tc.expectedFinalState, finalState, "Final state should match expected")
			})
		}
	})
}

// TestConfigurationValidation tests configuration validation
func TestConfigurationValidation(t *testing.T) {
	t.Run("Valid configuration", func(t *testing.T) {
		config := RetryConfig{
			Maxattempts:  30,
			DelaySeconds: 5,
			NonRetryableErrors: []string{
				"invalid address format",
				"token config not found",
			},
		}

		// Validate configuration
		assert.Greater(t, config.Maxattempts, 0, "Max attempts must be positive")
		assert.Greater(t, config.DelaySeconds, 0, "Delay seconds must be positive")
		assert.NotNil(t, config.NonRetryableErrors, "NonRetryableErrors must not be nil")

		// Validate that configuration doesn't contain insufficient balance patterns
		for _, pattern := range config.NonRetryableErrors {
			assert.NotContains(t, pattern, "insufficient", "Configuration should not contain insufficient balance patterns")
		}
	})

	t.Run("Configuration edge cases", func(t *testing.T) {
		testCases := []struct {
			name        string
			config      RetryConfig
			isValid     bool
			description string
		}{
			{
				name: "Zero max attempts",
				config: RetryConfig{
					Maxattempts:        0,
					DelaySeconds:       5,
					NonRetryableErrors: []string{"invalid address"},
				},
				isValid:     false,
				description: "Zero max attempts should be invalid",
			},
			{
				name: "Negative delay",
				config: RetryConfig{
					Maxattempts:        3,
					DelaySeconds:       -1,
					NonRetryableErrors: []string{"invalid address"},
				},
				isValid:     false,
				description: "Negative delay should be invalid",
			},
			{
				name: "Empty non-retryable errors",
				config: RetryConfig{
					Maxattempts:        3,
					DelaySeconds:       5,
					NonRetryableErrors: []string{},
				},
				isValid:     true,
				description: "Empty non-retryable errors should be valid",
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				// Validate configuration
				isValid := tc.config.Maxattempts > 0 && tc.config.DelaySeconds >= 0

				assert.Equal(t, tc.isValid, isValid, tc.description)
			})
		}
	})
}

// TestConfigurationCompatibility tests backward compatibility
func TestConfigurationCompatibility(t *testing.T) {
	t.Run("Legacy configuration handling", func(t *testing.T) {
		// Test that the system can handle legacy configurations that might still
		// contain insufficient balance patterns
		legacyNonRetryableErrors := []string{
			"invalid address format",
			"Insufficient hot wallet balance", // Legacy pattern (wrong case)
			"token config not found",
		}

		// The system should handle this gracefully by treating insufficient balance
		// as a special case regardless of configuration
		for _, pattern := range legacyNonRetryableErrors {
			if containsIgnoreCase(pattern, "insufficient") && containsIgnoreCase(pattern, "balance") {
				// This pattern should be ignored in favor of special handling
				assert.True(t, true, "Legacy insufficient balance patterns should be handled specially")
			}
		}
	})
}
