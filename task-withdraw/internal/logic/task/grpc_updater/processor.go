package grpc_updater

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	taskv1 "task-withdraw/api"
	"task-withdraw/internal/logic/task/withdrawal_processor"
	"task-withdraw/internal/service"

	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/gtime"
)

// ProcessMessage handles the parsing and processing of a single status update message.
func ProcessMessage(ctx context.Context, withdrawalClient service.IWithdrawalClient, redisClient *gredis.Redis, messageJson string, queueName, dlqName string, maxRetries int32, retryDelay time.Duration) {
	logPrefix := "[GrpcStatusUpdater.processMessage]"
	var msg withdrawal_processor.StatusUpdateMessage // Use the struct from withdrawal_processor

	err := json.Unmarshal([]byte(messageJson), &msg)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to unmarshal message JSON: %v. Moving to DLQ '%s'. Message: %s", logPrefix, err, dlqName, messageJson)
		MoveToDLQ(ctx, redisClient, dlqName, messageJson, "unmarshal_error")
		return
	}

	// Add withdrawal ID to log prefix for easier tracking
	logPrefix = fmt.Sprintf("%s[WithdrawalID:%d]", logPrefix, msg.WithdrawalID)

	// Construct the gRPC request
	// Map TargetState to ProcessingStatus since it represents withdrawal processing state
	updateReq := &taskv1.UpdateWithdrawalStatusRequest{
		WithdrawalId:     msg.WithdrawalID,
		ProcessingStatus: msg.TargetState, // Map TargetState to ProcessingStatus
		TxHash:           msg.TxHash,
		ErrorMessage:     msg.ErrorMessage, // Pass the full error history JSON
		Retries:          msg.Retries,      // Pass the retry count from the message (relevant for PENDING)
	}

	// Set auto_withdrawal_progress if provided
	if msg.AutoWithdrawalProgress != nil {
		updateReq.AutoWithdrawalProgress = *msg.AutoWithdrawalProgress
	}

	glog.Infof(ctx, "%s Attempting to update status via gRPC: State=%d, Retries=%d", logPrefix, msg.TargetState, msg.Retries)

	// Call gRPC service
	grpcErr := withdrawalClient.UpdateWithdrawalStatus(ctx, updateReq)

	if grpcErr != nil {
		glog.Errorf(ctx, "%s gRPC UpdateWithdrawalStatus failed: %v", logPrefix, grpcErr)

		// Increment retry count and update last attempt time
		msg.Retries++ // Note: This increments retries even for the final FAILED push, which is okay.
		msg.LastAttemptTime = gtime.Now().ISO8601()

		if msg.Retries <= maxRetries {
			glog.Warningf(ctx, "%s Scheduling retry %d/%d after delay %s.", logPrefix, msg.Retries, maxRetries, retryDelay)
			time.Sleep(retryDelay) // Simple delay before requeueing

			// Re-marshal the updated message
			updatedMsgBytes, marshalErr := json.Marshal(msg)
			if marshalErr != nil {
				glog.Errorf(ctx, "%s Failed to marshal updated message for retry: %v. Moving original message to DLQ '%s'. Original: %s", logPrefix, marshalErr, dlqName, messageJson)
				MoveToDLQ(ctx, redisClient, dlqName, messageJson, "retry_marshal_error")
				return
			}

			// Push back to the front of the main queue for retry
			_, lpushErr := redisClient.LPush(ctx, queueName, string(updatedMsgBytes))
			if lpushErr != nil {
				glog.Errorf(ctx, "%s Failed to LPush message back to queue '%s' for retry: %v. Moving original message to DLQ '%s'. Original: %s", logPrefix, lpushErr, queueName, dlqName, messageJson)
				MoveToDLQ(ctx, redisClient, dlqName, messageJson, "retry_lpush_error")
			} else {
				glog.Infof(ctx, "%s Message requeued for retry %d.", logPrefix, msg.Retries)
			}
		} else {
			glog.Errorf(ctx, "%s Max retries (%d) reached for withdrawal ID %d. Moving message to DLQ '%s'.", logPrefix, maxRetries, msg.WithdrawalID, dlqName)
			MoveToDLQ(ctx, redisClient, dlqName, messageJson, "max_retries_reached")
		}
		return // Exit processing for this message after handling error/retry
	}

	// gRPC call was successful
	glog.Infof(ctx, "%s Successfully processed status update message via gRPC for state %d.", logPrefix, msg.TargetState)
}
