grpc:
  client:
    user-service:
      endpoints:
        - "${TASK_WITHDRAW_GRPC_CLIENT_USER_SERVICE_ENDPOINTS}" # 替换成目标服务的实际地址和端口
      apiKeys: "${TASK_WITHDRAW_GRPC_CLIENT_USER_SERVICE_APIKEYS}"
      balancer: "${TASK_WITHDRAW_GRPC_CLIENT_USER_SERVICE_BALANCER}"
      timeout: ${TASK_WITHDRAW_GRPC_CLIENT_USER_SERVICE_TIMEOUT}

redis:
  default:
    address: "${TASK_WITHDRAW_REDIS_DEFAULT_ADDRESS}" # 确认或修改为您的 Redis 地址
    db: ${TASK_WITHDRAW_REDIS_DEFAULT_DB} # 确认或修改 DB 编号
    pass: "${TASK_WITHDRAW_REDIS_DEFAULT_PASS}" # 确认或修改密码
    idleTimeout: "${TASK_WITHDRAW_REDIS_DEFAULT_IDLETIMEOUT}" # 补充单位

logger:
  path: "${TASK_WITHDRAW_LOGGER_PATH}" # 统一日志目录，各应用可分子目录
  level: "${TASK_WITHDRAW_LOGGER_LEVEL}"
  stdout: ${TASK_WITHDRAW_LOGGER_STDOUT}
  rotateSize: "${TASK_WITHDRAW_LOGGER_ROTATESIZE}"
  rotateExpire: "${TASK_WITHDRAW_LOGGER_ROTATEEXPIRE}"
  format: "${TASK_WITHDRAW_LOGGER_FORMAT}" # 使用 JSON 格式方便收集

withdrawalProcessor:
  enabled: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_ENABLED}       # 启用提现处理任务 (Fetcher)
  spec: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_SPEC}" # Fetcher 运行频率 (例如每 10秒)
  batchSize: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_BATCHSIZE}         # Fetcher 每次获取数量

  # TRC20能量管理配置
  trc20EnergyManagement:
    enabled: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_ENABLED}                    # 启用TRC20能量管理
    bypassCheck: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_BYPASSCHECK}               # TEMPORARY: 绕过能量检查（RPC连接问题）
    energyReservePercent: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_ENERGYRESERVEPERCENT}       # 能量储备百分比 (10%)
    orderTimeoutHours: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_ORDERTIMEOUTHOURS}            # 订单超时时间(小时)
    maxRetryCount: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_MAXRETRYCOUNT}                 # 最大重试次数
    checkIntervalSeconds: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_CHECKINTERVALSECONDS}         # 订单状态检查间隔(秒)

    # iTRX API配置
    itrx:
      apiKey: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_ITRX_APIKEY}"           # iTRX API密钥
      apiSecret: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_ITRX_APISECRET}"     # iTRX API密钥
      apiBaseUrl: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_ITRX_APIBASEURL}"         # iTRX API基础URL
      energyPeriod: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_ITRX_ENERGYPERIOD}"                    # 能量租用时长 (1H/3H/1D等)
  # --- Processing Logic Configuration (Moved from withdrawalConsumer) ---
  enabledTokens: # 控制哪些代币的提现是启用的 (e.g., USDT_ERC20, USDT_TRC20)
    ETH: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_ENABLEDTOKENS_ETH}
    USDT_ERC20: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_ENABLEDTOKENS_USDT_ERC20}
    TRX: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_ENABLEDTOKENS_TRX}
    USDT_TRC20: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_ENABLEDTOKENS_USDT_TRC20}
  tokenContracts: # 代币合约地址配置
    USDT_ERC20: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TOKENCONTRACTS_USDT_ERC20}"
    USDT_TRC20: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TOKENCONTRACTS_USDT_TRC20}"
  tokenPrecisions: # 链上交易精度配置
    ETH: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TOKENPRECISIONS_ETH}
    USDT_ERC20: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TOKENPRECISIONS_USDT_ERC20}
    TRX: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TOKENPRECISIONS_TRX}
    USDT_TRC20: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TOKENPRECISIONS_USDT_TRC20}
  wallets:
    ETH:
      privateKey: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_WALLETS_ETH_PRIVATEKEY}" # 留空，优先使用环境变量
      privateKeyEnvVar: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_WALLETS_ETH_PRIVATEKEYENVVAR}"
      address: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_WALLETS_ETH_ADDRESS}"
    TRON:
      privateKey: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_WALLETS_TRON_PRIVATEKEY}" # 留空，优先使用环境变量
      privateKeyEnvVar: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_WALLETS_TRON_PRIVATEKEYENVVAR}"
      address: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_WALLETS_TRON_ADDRESS}"
  rpc:
    ETH:
      url: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_RPC_ETH_URL}"
      chainId: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_RPC_ETH_CHAINID}
    TRON:
      url: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_RPC_TRON_URL}"
      apiKey: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_RPC_TRON_APIKEY}"
      callTimeoutSeconds: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_RPC_TRON_CALLTIMEOUTSECONDS}
  retry:
    maxAttempts: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_RETRY_MAXATTEMPTS}
    nonRetryableErrors:
      - "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_RETRY_NONRETRYABLEERRORS_1}"
      - "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_RETRY_NONRETRYABLEERRORS_2}"


# --- Withdrawal Consumer Worker Configuration ---
withdrawalConsumer:
  enabled: ${TASK_WITHDRAW_WITHDRAWALCONSUMER_ENABLED}         # 启用 Consumer Worker
  redisQueueName: "${TASK_WITHDRAW_WITHDRAWALCONSUMER_REDISQUEUENAME}" # 消费队列名称
  concurrency: ${TASK_WITHDRAW_WITHDRAWALCONSUMER_CONCURRENCY}        # 启动 1 个串行 Worker
  dlqName: "${TASK_WITHDRAW_WITHDRAWALCONSUMER_DLQNAME}" # 死信队列名称
  # Note: Processing logic configuration moved to withdrawalProcessor

# --- gRPC Status Updater Worker Configuration ---
grpcUpdater:
  redisQueueName: "${TASK_WITHDRAW_GRPCUPDATER_REDISQUEUENAME}" # 主处理队列名称
  redisDlqName: "${TASK_WITHDRAW_GRPCUPDATER_REDISDLQNAME}" # 死信队列名称
  brpopTimeoutSeconds: ${TASK_WITHDRAW_GRPCUPDATER_BRPOPTIMEOUTSECONDS} # Redis BRPop 阻塞超时时间 (秒)
  maxRetries: ${TASK_WITHDRAW_GRPCUPDATER_MAXRETRIES} # gRPC 调用最大重试次数
  retryDelaySeconds: ${TASK_WITHDRAW_GRPCUPDATER_RETRYDELAYSECONDS} # gRPC 调用重试间隔 (秒)

# Consul 配置（用于配置同步）
consul:
  address: "${TASK_WITHDRAW_CONSUL_ADDRESS}" # Consul 服务器地址
  token: "${TASK_WITHDRAW_CONSUL_TOKEN}" # ACL Token（生产环境请修改）
  config_prefix: "${TASK_WITHDRAW_CONSUL_CONFIG_PREFIX}" # 配置存储前缀