package service

import (
	"context"
	"task-api/internal/model/entity"
)

// 引入 protobuf 定义

// WithdrawalType 定义提币记录类型
type WithdrawalType int32

const (
	WithdrawalTypeUnspecified WithdrawalType = 0 // 未指定
	WithdrawalTypeUser        WithdrawalType = 1 // 用户提币 (user_withdraws)
	WithdrawalTypeMerchant    WithdrawalType = 2 // 商户提币 (merchant_withdraws)
	WithdrawalTypeSettlement  WithdrawalType = 3 // 商户结算 (merchant_settlements)
)

// UnifiedWithdrawal 统一的提币记录结构
type UnifiedWithdrawal struct {
	*entity.UserWithdraws                // 嵌入用户提币实体
	WithdrawalType        WithdrawalType `json:"withdrawal_type"` // 提币类型
	SubType               int32          `json:"sub_type"`        // 子类型 (withdraws_type/settlements_type)
	State                 int32          `json:"state"`           // 商户表状态字段
	MerchantId            uint64         `json:"merchant_id"`     // 商户ID (用于商户表)
}

// ITask 定义了任务服务的接口
type ITask interface {
	// ListUserWithdraws 获取用户提现记录列表（带分页和筛选）
	ListUserWithdraws(ctx context.Context, page, pageSize int, filter map[string]interface{}) (list []*entity.UserWithdraws, total int, err error)

	// ListUnifiedWithdrawals 获取统一的提币记录列表（支持多表合并）
	ListUnifiedWithdrawals(ctx context.Context, page, pageSize int, filter map[string]interface{}) (list []*UnifiedWithdrawal, total int, err error)

	// UpdateUserWithdrawStatus 更新用户提现记录状态和相关字段
	UpdateUserWithdrawStatus(ctx context.Context, id uint, status uint, data map[string]interface{}) error

	// UpdateUnifiedWithdrawalStatus 更新统一提币记录状态（支持多表）
	UpdateUnifiedWithdrawalStatus(ctx context.Context, id uint64, withdrawalType WithdrawalType, data map[string]interface{}) error
}

// No implementation details here.

var localTask ITask

// RegisterTask 注册 Task 服务实现
func RegisterTask(i ITask) {
	localTask = i
}

// Task 获取 Task 服务实例
func Task() ITask {
	if localTask == nil {
		panic("implement not found for interface ITask, forgot register?")
	}
	return localTask
}
