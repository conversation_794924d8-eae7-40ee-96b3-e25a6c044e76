syntax = "proto3";

package task.v1; // 使用版本化的包名

import "google/protobuf/timestamp.proto"; // 用于时间戳
// 移除: import "google/protobuf/any.proto";
import "google/protobuf/empty.proto";     // 添加: 用于无数据成功响应

// 指定 Go 包路径和别名，将在 task-server/api/task/v1 下生成代码
option go_package = "task-server/api/task/v1;taskv1";

// 提币记录类型枚举
enum WithdrawalType {
  WITHDRAWAL_TYPE_UNSPECIFIED = 0; // 未指定
  WITHDRAWAL_TYPE_USER = 1;        // 用户提币 (user_withdraws)
  WITHDRAWAL_TYPE_MERCHANT = 2;    // 商户提币 (merchant_withdraws)
  WITHDRAWAL_TYPE_SETTLEMENT = 3;  // 商户结算 (merchant_settlements)
}

// 提币记录消息 (支持多表合并)
// 注意: 支持 user_withdraws, merchant_withdraws, merchant_settlements 三个表
message Withdrawal {
  int64 user_withdraws_id = 1; // 统一主键ID (映射到各表的主键)
  int64 user_id = 2;           // 用户/商户ID (user_id/merchant_id)
  int64 token_id = 3;          // uint -> int64
  string wallet_id = 4;
  string name = 5;
  string chan = 6;
  string order_no = 7;
  string address = 8;
  string recipient_name = 9;    // 法币字段，保留
  string recipient_account = 10; // 法币字段，保留
  double amount = 11;
  double handling_fee = 12;
  double actual_amount = 13;
  int32 audit_status = 14;             // 审核状态: 1-免审, 2-待审核, 3-审核通过, 4-审核拒绝 (仅user_withdraws)
  int32 auto_withdrawal_progress = 15; // 自动提现状态 0 未开始 1 进行中 2 成功 3 结束 (仅user_withdraws)
  int32 processing_status = 16;        // 提现处理状态: 1-自动放币处理中, 2-处理中(待冷钱包转入)，3.待人工转账.，4-成功, 5-失败 (仅user_withdraws)
  int32 state = 17;                    // 商户表状态: 1-待审核, 2-处理中, 3-已拒绝, 4-已完成, 5-失败 (merchant_withdraws/settlements)
  WithdrawalType withdrawal_type = 18; // 提币记录类型
  int32 sub_type = 19;                 // 子类型 (withdraws_type/settlements_type)
  string refuse_reason_zh = 20;
  string refuse_reason_en = 21;
  string tx_hash = 22;
  string error_message = 23; // 注意：DB 中可能是 JSON 字符串
  string user_remark = 24;
  string admin_remark = 25;
  google.protobuf.Timestamp created_at = 26;
  google.protobuf.Timestamp checked_at = 27;
  google.protobuf.Timestamp processing_at = 28;
  google.protobuf.Timestamp completed_at = 29;
  google.protobuf.Timestamp updated_at = 30;
  int32 retries = 31;
  int32 nergy_state = 32;
  string recipient_qrcode = 33; // 法币收款二维码
  string fiat_type = 34;        // 法币提现类型
}

// ListWithdrawals 请求
message ListWithdrawalsRequest {
  int32 page_size = 1; // 每页数量 (建议 > 0)
  int32 page = 2;      // 页码 (建议 >= 1)
  // 可选过滤条件
  int32 filter_audit_status = 3;           // 按审核状态过滤 (0 表示不过滤, 仅user_withdraws)
  int32 filter_processing_status = 4;      // 按处理状态过滤 (0 表示不过滤, 仅user_withdraws)
  int32 filter_state = 5;                  // 按商户表状态过滤 (0 表示不过滤, merchant_withdraws/settlements)
  int64 filter_user_id = 6;                // 按用户/商户ID过滤 (0 表示不过滤)
  string filter_order_no = 7;              // 按订单号过滤 (空字符串表示不过滤)
  WithdrawalType filter_withdrawal_type = 8; // 按提币类型过滤 (UNSPECIFIED 表示不过滤)
  int32 filter_sub_type = 9;               // 按子类型过滤 (0 表示不过滤)
  // 可以添加更多过滤，如 token_id, address, 时间范围等
}

// ListWithdrawals 响应
message ListWithdrawalsResponse {
  repeated Withdrawal withdrawals = 1; // 提币记录列表
  int32 total_count = 2;             // 总记录数
  int32 current_page = 3;            // 当前页码
  int32 total_pages = 4;             // 总页数
}

// UpdateWithdrawalStatus 请求
message UpdateWithdrawalStatusRequest {
  int64 withdrawal_id = 1;                    // 提币记录ID
  WithdrawalType withdrawal_type = 2;         // 必需：提币记录类型，用于确定更新哪个表
  int32 audit_status = 3;                     // 可选：审核状态: 1-免审, 2-待审核, 3-审核通过, 4-审核拒绝 (仅user_withdraws)
  int32 auto_withdrawal_progress = 4;         // 可选：自动提现状态 0 未开始 1 进行中 2 成功 3 结束 (仅user_withdraws)
  int32 processing_status = 5;                // 可选：提现处理状态: 1-自动放币处理中, 2-处理中(待冷钱包转入)，3.待人工转账.，4-成功, 5-失败 (仅user_withdraws)
  int32 state = 6;                            // 可选：商户表状态: 1-待审核, 2-处理中, 3-已拒绝, 4-已完成, 5-失败 (merchant_withdraws/settlements)
  string error_message = 7;                   // 可选：错误信息（JSON字符串）
  string refuse_reason_zh = 8;                // 可选：拒绝原因（中文）
  string refuse_reason_en = 9;                // 可选：拒绝原因（英文）
  string tx_hash = 10;                        // 可选：交易哈希
  int32 retries = 11;                         // 可选：重试次数
  string admin_remark = 12;                   // 可选：管理员备注
}

// 通用 API 响应结构 (使用 oneof)
message ApiResponse {
  int32 code = 1;       // 业务状态码
  string message = 2;   // 响应消息
  oneof data_payload { // 定义 oneof 字段
    ListWithdrawalsResponse list_withdrawals_data = 3; // 用于 ListWithdrawals 成功响应
    google.protobuf.Empty success_no_data = 4;       // 用于 UpdateWithdrawalStatus 等无数据成功响应
  }
}

// TaskService 服务定义
service TaskService {
  // 获取提币记录列表 (带过滤和分页)
  // 返回通用响应结构，data 字段包含 ListWithdrawalsResponse
  rpc ListWithdrawals(ListWithdrawalsRequest) returns (ApiResponse);

  // 更新提币记录状态和字段
  // 返回通用响应结构，data 字段包含更新后的 Withdrawal (如果成功且需要返回) 或为空
  rpc UpdateWithdrawalStatus(UpdateWithdrawalStatusRequest) returns (ApiResponse);
}